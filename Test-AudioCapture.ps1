# AudioCaptureService Testing Script
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("quick", "detailed", "performance", "troubleshoot")]
    [string]$TestType = "quick",
    
    [Parameter(Mandatory=$false)]
    [int]$Duration = 5,
    
    [Parameter(Mandatory=$false)]
    [switch]$ShowLogs
)

Write-Host "=== AudioCaptureService Test Script ===" -ForegroundColor Green
Write-Host "Test Type: $TestType" -ForegroundColor Yellow
Write-Host "Duration: $Duration seconds" -ForegroundColor Yellow

function Test-Prerequisites {
    Write-Host "`nChecking prerequisites..." -ForegroundColor Cyan
    
    # Check .NET
    try {
        $dotnetVersion = dotnet --version
        Write-Host "✓ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ .NET SDK not found" -ForegroundColor Red
        exit 1
    }
    
    # Check audio devices
    Write-Host "Checking audio devices..." -ForegroundColor Cyan
    $audioDevices = Get-WmiObject -Class Win32_SoundDevice | Where-Object { $_.Status -eq "OK" }
    if ($audioDevices.Count -gt 0) {
        Write-Host "✓ Found $($audioDevices.Count) audio device(s)" -ForegroundColor Green
        foreach ($device in $audioDevices) {
            Write-Host "  - $($device.Name)" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "⚠ No audio devices found" -ForegroundColor Yellow
    }
    
    # Check microphone access
    Write-Host "Checking microphone permissions..." -ForegroundColor Cyan
    # Note: This is a simplified check - actual permission testing requires running the audio capture
    Write-Host "✓ Microphone permission check will be done during audio test" -ForegroundColor Green
}

function Start-QuickTest {
    Write-Host "`n--- Quick Audio Test ---" -ForegroundColor Cyan
    Write-Host "This will test basic audio capture functionality" -ForegroundColor White
    
    try {
        Write-Host "Building project..." -ForegroundColor Yellow
        dotnet build InterviewAssistant.Service.csproj --verbosity quiet
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Build successful" -ForegroundColor Green
            
            Write-Host "`nRunning audio capture test..." -ForegroundColor Yellow
            Write-Host "Please speak into your microphone and play some system audio" -ForegroundColor Cyan
            
            # Run the audio test
            dotnet run --project . AudioCaptureTest.cs
        }
        else {
            Write-Host "✗ Build failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Quick test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Start-DetailedTest {
    Write-Host "`n--- Detailed Audio Test ---" -ForegroundColor Cyan
    Write-Host "This will run comprehensive audio capture tests" -ForegroundColor White
    
    # Build first
    Write-Host "Building project..." -ForegroundColor Yellow
    dotnet build InterviewAssistant.Service.csproj
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Build failed" -ForegroundColor Red
        return $false
    }
    
    # Test 1: Audio device detection
    Write-Host "`nTest 1: Audio Device Detection" -ForegroundColor Magenta
    dotnet run --project . AudioCaptureTest.cs 2>&1 | Select-String "device|Device" | ForEach-Object {
        Write-Host "  $_" -ForegroundColor Gray
    }
    
    # Test 2: Audio capture functionality
    Write-Host "`nTest 2: Audio Capture Functionality" -ForegroundColor Magenta
    Write-Host "Starting $Duration second audio capture test..." -ForegroundColor Yellow
    Write-Host "Instructions:" -ForegroundColor Cyan
    Write-Host "1. Speak into your microphone" -ForegroundColor White
    Write-Host "2. Play music or video on your computer" -ForegroundColor White
    Write-Host "3. If possible, join a video call (Google Meet/Teams)" -ForegroundColor White
    
    $testStart = Get-Date
    dotnet run --project . AudioCaptureTest.cs
    $testEnd = Get-Date
    $actualDuration = ($testEnd - $testStart).TotalSeconds
    
    Write-Host "`nTest completed in $([math]::Round($actualDuration, 1)) seconds" -ForegroundColor Green
    
    return $true
}

function Start-PerformanceTest {
    Write-Host "`n--- Performance Test ---" -ForegroundColor Cyan
    Write-Host "This will monitor CPU and memory usage during audio capture" -ForegroundColor White
    
    # Build first
    dotnet build InterviewAssistant.Service.csproj --verbosity quiet
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Build failed" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Starting performance monitoring..." -ForegroundColor Yellow
    Write-Host "Test duration: $Duration seconds" -ForegroundColor Cyan
    
    # Start the audio test in background
    $job = Start-Job -ScriptBlock {
        param($projectPath)
        Set-Location $projectPath
        dotnet run --project . AudioCaptureTest.cs
    } -ArgumentList (Get-Location).Path
    
    # Monitor performance
    $startTime = Get-Date
    $samples = @()
    
    while ((Get-Date) -lt $startTime.AddSeconds($Duration + 5)) {
        try {
            $processes = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue
            if ($processes) {
                $totalCPU = ($processes | Measure-Object CPU -Sum).Sum
                $totalMemory = ($processes | Measure-Object WorkingSet -Sum).Sum / 1MB
                
                $sample = [PSCustomObject]@{
                    Time = Get-Date
                    CPU = $totalCPU
                    MemoryMB = [math]::Round($totalMemory, 1)
                }
                $samples += $sample
                
                Write-Host "CPU: $([math]::Round($totalCPU, 1))%, Memory: $($sample.MemoryMB) MB" -ForegroundColor Gray
            }
        }
        catch {
            # Ignore errors during monitoring
        }
        
        Start-Sleep -Seconds 1
    }
    
    # Stop the job
    Stop-Job $job -ErrorAction SilentlyContinue
    Remove-Job $job -ErrorAction SilentlyContinue
    
    # Analyze performance
    if ($samples.Count -gt 0) {
        $avgMemory = ($samples | Measure-Object MemoryMB -Average).Average
        $maxMemory = ($samples | Measure-Object MemoryMB -Maximum).Maximum
        
        Write-Host "`nPerformance Summary:" -ForegroundColor Green
        Write-Host "  Average Memory: $([math]::Round($avgMemory, 1)) MB" -ForegroundColor White
        Write-Host "  Peak Memory: $([math]::Round($maxMemory, 1)) MB" -ForegroundColor White
        
        if ($maxMemory -lt 100) {
            Write-Host "  ✓ Memory usage: GOOD" -ForegroundColor Green
        }
        elseif ($maxMemory -lt 200) {
            Write-Host "  ⚠ Memory usage: MODERATE" -ForegroundColor Yellow
        }
        else {
            Write-Host "  ✗ Memory usage: HIGH" -ForegroundColor Red
        }
    }
    
    return $true
}

function Start-TroubleshootTest {
    Write-Host "`n--- Troubleshooting Test ---" -ForegroundColor Cyan
    Write-Host "This will help diagnose audio capture issues" -ForegroundColor White
    
    # Check Windows Audio service
    Write-Host "`nChecking Windows Audio service..." -ForegroundColor Yellow
    $audioService = Get-Service -Name "AudioSrv" -ErrorAction SilentlyContinue
    if ($audioService) {
        Write-Host "✓ Windows Audio service: $($audioService.Status)" -ForegroundColor Green
    }
    else {
        Write-Host "✗ Windows Audio service not found" -ForegroundColor Red
    }
    
    # Check audio devices in detail
    Write-Host "`nDetailed audio device information..." -ForegroundColor Yellow
    Get-WmiObject -Class Win32_SoundDevice | Select-Object Name, Status, Manufacturer | Format-Table -AutoSize
    
    # Try to build and run a minimal test
    Write-Host "`nTesting basic audio initialization..." -ForegroundColor Yellow
    try {
        dotnet build InterviewAssistant.Service.csproj --verbosity minimal
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Project builds successfully" -ForegroundColor Green
            
            # Run a very short test
            Write-Host "Running 2-second audio test..." -ForegroundColor Yellow
            $output = dotnet run --project . AudioCaptureTest.cs 2>&1
            
            # Analyze output for common issues
            if ($output -match "Audio capture initialized successfully") {
                Write-Host "✓ Audio initialization: SUCCESS" -ForegroundColor Green
            }
            else {
                Write-Host "✗ Audio initialization: FAILED" -ForegroundColor Red
                Write-Host "Error details:" -ForegroundColor Yellow
                $output | Where-Object { $_ -match "error|Error|exception|Exception" } | ForEach-Object {
                    Write-Host "  $_" -ForegroundColor Red
                }
            }
        }
        else {
            Write-Host "✗ Project build failed" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ Troubleshooting test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Show recent logs if available
    if (Test-Path "logs") {
        Write-Host "`nRecent log entries:" -ForegroundColor Yellow
        Get-ChildItem "logs\*.txt" | Sort-Object LastWriteTime -Descending | Select-Object -First 1 | ForEach-Object {
            Write-Host "From: $($_.Name)" -ForegroundColor Gray
            Get-Content $_.FullName -Tail 10 | ForEach-Object {
                Write-Host "  $_" -ForegroundColor Gray
            }
        }
    }
    
    return $true
}

# Main execution
Test-Prerequisites

$success = switch ($TestType) {
    "quick" { Start-QuickTest }
    "detailed" { Start-DetailedTest }
    "performance" { Start-PerformanceTest }
    "troubleshoot" { Start-TroubleshootTest }
}

if ($ShowLogs -and (Test-Path "logs")) {
    Write-Host "`n--- Recent Logs ---" -ForegroundColor Cyan
    Get-ChildItem "logs\*.txt" | Sort-Object LastWriteTime -Descending | Select-Object -First 1 | ForEach-Object {
        Write-Host "Showing last 20 lines from: $($_.Name)" -ForegroundColor Yellow
        Get-Content $_.FullName -Tail 20
    }
}

Write-Host "`n=== Test Completed ===" -ForegroundColor Green
if ($success) {
    Write-Host "Result: SUCCESS" -ForegroundColor Green
}
else {
    Write-Host "Result: ISSUES FOUND" -ForegroundColor Yellow
    Write-Host "Check the output above for details" -ForegroundColor White
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "- For more detailed testing: .\Test-AudioCapture.ps1 -TestType detailed" -ForegroundColor White
Write-Host "- For performance testing: .\Test-AudioCapture.ps1 -TestType performance -Duration 30" -ForegroundColor White
Write-Host "- For troubleshooting: .\Test-AudioCapture.ps1 -TestType troubleshoot" -ForegroundColor White
Write-Host "- To view logs: .\Test-AudioCapture.ps1 -ShowLogs" -ForegroundColor White
