{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "InterviewAssistant.Service.Services.AzureSpeechRecognizer": "Debug"}}, "Azure": {"SpeechKey": "7BYgJa31VkLQfn2DDyU1qJhzn267vcx00OtM7pbacLzjbIwhRk1KJQQJ99BGACYeBjFXJ3w3AAAYACOGVFI9", "SpeechRegion": "eastus", "Language": "en-US"}, "Backend": {"BaseUrl": "https://httpbin.org", "AuthEndpoint": "/post", "TranscriptEndpoint": "/post", "Username": "test-user", "Password": "test-password", "JwtToken": "test-token-123"}, "Session": {"SessionId": "", "SpeakerId": "user", "AutoGenerateSessionId": true}, "Audio": {"SampleRate": 16000, "Channels": 1, "BitsPerSample": 16, "BufferMilliseconds": 100}, "Service": {"MaxRetryAttempts": 3, "RetryDelaySeconds": 2, "HeartbeatIntervalSeconds": 30}}