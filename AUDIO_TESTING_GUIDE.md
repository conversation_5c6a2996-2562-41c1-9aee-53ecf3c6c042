# AudioCaptureService Testing Guide

This guide provides comprehensive steps to test the dual audio capture functionality (microphone + system audio).

## Prerequisites

1. **Hardware Requirements:**
   - Working microphone (built-in or external)
   - Audio output device (speakers/headphones)
   - Windows 10/11 with audio drivers installed

2. **Software Requirements:**
   - .NET 8.0 SDK installed
   - Visual Studio Code or Visual Studio (optional)
   - Audio source for testing (music, video, or video call)

3. **Permissions:**
   - Microphone access enabled for the application
   - System audio access (usually automatic)

## Quick Test Steps

### Step 1: Basic Build and Functionality Test

```powershell
# 1. Build the project
dotnet build InterviewAssistant.Service.csproj

# 2. Run the dedicated audio capture test
dotnet run --project . AudioCaptureTest.cs
```

**Expected Output:**
```
=== AudioCaptureService Dual Audio Test ===
[INFO] Initializing dual audio capture service (Microphone + System Audio)
[INFO] Using microphone device: Microphone (Realtek Audio)
[INFO] Using system audio device: Speakers (Realtek Audio)
✓ Audio capture initialized successfully
```

### Step 2: Interactive Audio Test

```powershell
# Run the interactive test
dotnet run AudioCaptureTest.cs
```

**During the test:**
1. **Speak into your microphone** - say "Testing microphone audio"
2. **Play system audio** - open YouTube, Spotify, or any audio
3. **Optional:** Join a Google Meet/Teams call to test real scenario

**Expected Results:**
```
📊 Received 50 audio events, 25,600 total bytes
✓ Audio capture quality: GOOD
- Events per second: 10.2
- Estimated data rate: 5,120 bytes/second
```

### Step 3: Console Mode Testing

```powershell
# Run the full service in console mode
.\Test-Service.ps1 -Action run
```

**What to test:**
1. Service starts without errors
2. Audio capture initializes for both microphone and system audio
3. Audio data is being processed continuously
4. No memory leaks during extended operation

## Detailed Testing Scenarios

### Scenario 1: Microphone Only Test

1. **Disable system audio** (mute speakers)
2. **Run the test** and speak into microphone
3. **Verify:** Only microphone audio is captured

```powershell
# Check logs for microphone-specific messages
Get-Content "logs\audio-capture-test-*.txt" | Select-String "Microphone"
```

### Scenario 2: System Audio Only Test

1. **Mute microphone** in Windows settings
2. **Play music/video** on your computer
3. **Run the test**
4. **Verify:** Only system audio is captured

### Scenario 3: Dual Audio Test (Real Interview Scenario)

1. **Join a Google Meet/Teams call** (or simulate with two browser tabs)
2. **Play audio** from the call (other participants speaking)
3. **Speak into microphone** (your voice)
4. **Run the test**
5. **Verify:** Both audio sources are captured simultaneously

### Scenario 4: Error Handling Test

1. **Disconnect microphone** during capture
2. **Verify:** Service handles the error gracefully
3. **Reconnect microphone**
4. **Verify:** Service recovers automatically

## Performance Testing

### Memory Usage Test

```powershell
# Run service and monitor memory
dotnet run AudioCaptureTest.cs &
Get-Process "dotnet" | Select-Object ProcessName, WorkingSet, CPU
```

**Expected:** Memory usage should remain stable (< 100MB)

### CPU Usage Test

```powershell
# Monitor CPU usage during audio capture
Get-Counter "\Process(dotnet)\% Processor Time" -SampleInterval 1 -MaxSamples 30
```

**Expected:** CPU usage should be low (< 10% on modern systems)

### Audio Latency Test

1. **Speak into microphone**
2. **Check timestamps** in logs
3. **Measure delay** between speech and audio data events

**Expected:** Latency should be < 100ms for real-time processing

## Troubleshooting

### Common Issues and Solutions

#### 1. "Audio device not found"
```
✗ Audio initialization failed: Audio device not found
```

**Solutions:**
- Check microphone is connected and enabled
- Run `mmsys.cpl` to verify recording devices
- Ensure microphone permissions are granted

#### 2. "No audio data captured"
```
✗ No audio data captured - check audio devices
```

**Solutions:**
- Verify microphone is not muted
- Check Windows audio levels
- Test microphone in other applications
- Ensure system audio is playing

#### 3. "System audio capture failed"
```
✗ System audio recording stopped due to error
```

**Solutions:**
- Check if other applications are using exclusive audio mode
- Restart Windows Audio service: `net stop audiosrv && net start audiosrv`
- Update audio drivers

#### 4. Low audio quality
```
⚠ Audio capture quality: LOW
```

**Solutions:**
- Check microphone input levels
- Verify sample rate settings in appsettings.json
- Test with different audio devices

### Debug Commands

```powershell
# Check audio devices
Get-WmiObject -Class Win32_SoundDevice | Select-Object Name, Status

# Check Windows Audio service
Get-Service -Name "AudioSrv"

# View detailed logs
Get-Content "logs\audio-capture-test-*.txt" -Tail 50 -Wait

# Test network connectivity (if using Azure Speech)
Test-NetConnection speech.platform.bing.com -Port 443
```

## Configuration Testing

### Audio Settings in appsettings.json

```json
{
  "Audio": {
    "SampleRate": 16000,     // Test with 16000, 22050, 44100
    "Channels": 1,           // Test with 1 (mono) or 2 (stereo)
    "BitsPerSample": 16,     // Test with 16 or 24
    "BufferMilliseconds": 100 // Test with 50, 100, 200
  }
}
```

**Test different configurations:**
1. Change sample rate and verify audio quality
2. Test mono vs stereo capture
3. Adjust buffer size for latency vs stability

## Success Criteria

✅ **Initialization:** Both microphone and system audio devices detected  
✅ **Dual Capture:** Simultaneous capture from both sources  
✅ **Data Flow:** Consistent audio data events (>5 events/second)  
✅ **Quality:** Good audio data rate (>1000 bytes/event average)  
✅ **Stability:** No crashes during 5+ minute operation  
✅ **Error Handling:** Graceful handling of device disconnection  
✅ **Performance:** Low CPU (<10%) and stable memory usage  
✅ **Cleanup:** Proper resource disposal on shutdown  

## Next Steps

After successful testing:
1. **Integration Testing:** Test with Azure Speech Recognition
2. **Load Testing:** Extended operation (30+ minutes)
3. **Production Testing:** Deploy and test in real interview scenarios
4. **Monitoring:** Set up logging and alerting for production use

## Advanced Testing

### Custom Test Scenarios

Create your own test scenarios by modifying `AudioCaptureTest.cs`:

```csharp
// Example: Test specific duration
await TestAudioCapture(audioService, durationSeconds: 30);

// Example: Test with custom event handlers
audioService.AudioDataAvailable += CustomAudioHandler;
```

### Automated Testing

```powershell
# Run automated test suite
.\Test-Service.ps1 -Action test

# Run specific audio tests
dotnet test --filter "Category=AudioCapture"
```
