# Quick Transcription Test
Write-Host "=== Quick Transcription Test ===" -ForegroundColor Green

# Check if service is running
$dotnetProcess = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue
if (-not $dotnetProcess) {
    Write-Host "Starting the service..." -ForegroundColor Yellow
    Start-Process -FilePath "dotnet" -ArgumentList "run" -NoNewWindow -PassThru
    Write-Host "Waiting for service to start..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

Write-Host "`nService should be running. Now:" -ForegroundColor Cyan
Write-Host "1. Speak clearly into your microphone" -ForegroundColor White
Write-Host "2. Say: 'Hello, this is a transcription test'" -ForegroundColor White
Write-Host "3. Watch below for transcriptions..." -ForegroundColor White
Write-Host "`nMonitoring for transcriptions (30 seconds):" -ForegroundColor Yellow

# Monitor logs for transcriptions
$logFile = Get-ChildItem "logs\interview-assistant-*.txt" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
if ($logFile) {
    Write-Host "Monitoring: $($logFile.Name)" -ForegroundColor Gray
    
    $startTime = Get-Date
    $foundTranscriptions = 0
    
    while ((Get-Date) -lt $startTime.AddSeconds(30)) {
        $newLines = Get-Content $logFile.FullName -Tail 5
        
        foreach ($line in $newLines) {
            if ($line -match "Recognizing:|Recognized:") {
                $foundTranscriptions++
                Write-Host "🎙️ $line" -ForegroundColor Green
            }
            elseif ($line -match "ERROR|Error") {
                Write-Host "❌ $line" -ForegroundColor Red
            }
        }
        
        Start-Sleep -Milliseconds 1000
    }
    
    if ($foundTranscriptions -eq 0) {
        Write-Host "`n⚠️ No transcriptions found. Try:" -ForegroundColor Yellow
        Write-Host "1. Speaking louder and clearer" -ForegroundColor White
        Write-Host "2. Checking microphone levels" -ForegroundColor White
        Write-Host "3. Running: .\Diagnose-Transcription.ps1" -ForegroundColor White
    }
    else {
        Write-Host "`n✅ Found $foundTranscriptions transcription(s)! The service is working." -ForegroundColor Green
    }
}
else {
    Write-Host "❌ No log file found. Make sure the service is running." -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
