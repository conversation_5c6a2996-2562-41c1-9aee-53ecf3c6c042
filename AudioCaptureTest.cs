using InterviewAssistant.Service.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Diagnostics;

namespace InterviewAssistant.Service.Test;

/// <summary>
/// Comprehensive test program for AudioCaptureService dual audio capture functionality
/// Tests both microphone and system audio capture
/// </summary>
public class AudioCaptureTest
{
    private static readonly List<string> _capturedAudioLogs = new();
    private static int _microphoneDataCount = 0;
    private static int _systemAudioDataCount = 0;
    private static long _totalBytesReceived = 0;

    public static async Task Main(string[] args)
    {
        // Configure detailed logging for audio testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File("logs/audio-capture-test-.txt", 
                rollingInterval: RollingInterval.Day,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        try
        {
            Console.WriteLine("=== AudioCaptureService Dual Audio Test ===");
            Console.WriteLine("This will test microphone + system audio capture");
            Console.WriteLine("Make sure you have:");
            Console.WriteLine("1. A working microphone");
            Console.WriteLine("2. System audio playing (music, video, etc.)");
            Console.WriteLine("3. Google Meet/Teams call running (optional)");
            Console.WriteLine();

            // Setup DI container
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile("appsettings.Test.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            services.AddLogging(builder => builder.AddSerilog());
            services.AddSingleton<AudioCaptureService>();

            var serviceProvider = services.BuildServiceProvider();

            // Run comprehensive audio capture tests
            await RunAudioCaptureTests(serviceProvider);

            Console.WriteLine("\n=== Audio Capture Test Completed ===");
            Console.WriteLine("Check the logs for detailed information.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test failed with error: {ex.Message}");
            Log.Fatal(ex, "Audio capture test terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static async Task RunAudioCaptureTests(ServiceProvider serviceProvider)
    {
        var audioService = serviceProvider.GetRequiredService<AudioCaptureService>();
        
        Console.WriteLine("\n--- Test 1: Audio Service Initialization ---");
        await TestAudioInitialization(audioService);
        
        Console.WriteLine("\n--- Test 2: Short Audio Capture (5 seconds) ---");
        await TestShortAudioCapture(audioService);
        
        Console.WriteLine("\n--- Test 3: Audio Data Analysis ---");
        AnalyzeAudioData();
        
        Console.WriteLine("\n--- Test 4: Service Cleanup ---");
        TestServiceCleanup(audioService);
    }

    private static async Task TestAudioInitialization(AudioCaptureService audioService)
    {
        try
        {
            Console.WriteLine("Initializing dual audio capture...");
            await audioService.InitializeAsync();
            Console.WriteLine("✓ Audio capture initialized successfully");
            Console.WriteLine("  - Microphone capture: Ready");
            Console.WriteLine("  - System audio capture: Ready");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Audio initialization failed: {ex.Message}");
            throw;
        }
    }

    private static async Task TestShortAudioCapture(AudioCaptureService audioService)
    {
        try
        {
            // Subscribe to audio events with detailed logging
            audioService.AudioDataAvailable += OnAudioDataReceived;

            Console.WriteLine("Starting audio capture...");
            Console.WriteLine("Please:");
            Console.WriteLine("1. Speak into your microphone");
            Console.WriteLine("2. Play some system audio (music/video)");
            Console.WriteLine("3. If possible, join a Google Meet/Teams call");
            Console.WriteLine();

            var stopwatch = Stopwatch.StartNew();
            await audioService.StartCaptureAsync();

            // Capture for 5 seconds with progress indication
            for (int i = 5; i > 0; i--)
            {
                Console.WriteLine($"Capturing audio... {i} seconds remaining");
                await Task.Delay(1000);
            }

            await audioService.StopCaptureAsync();
            stopwatch.Stop();

            Console.WriteLine($"✓ Audio capture completed in {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine($"  - Total audio events: {_capturedAudioLogs.Count}");
            Console.WriteLine($"  - Total bytes received: {_totalBytesReceived:N0}");
            Console.WriteLine($"  - Average bytes per event: {(_capturedAudioLogs.Count > 0 ? _totalBytesReceived / _capturedAudioLogs.Count : 0):N0}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Audio capture test failed: {ex.Message}");
            throw;
        }
    }

    private static void OnAudioDataReceived(object? sender, AudioDataEventArgs e)
    {
        _totalBytesReceived += e.AudioData.Length;
        var logMessage = $"Audio data: {e.AudioData.Length} bytes at {e.Timestamp:HH:mm:ss.fff}";
        _capturedAudioLogs.Add(logMessage);

        // Log every 10th event to avoid spam
        if (_capturedAudioLogs.Count % 10 == 0)
        {
            Console.WriteLine($"  📊 Received {_capturedAudioLogs.Count} audio events, {_totalBytesReceived:N0} total bytes");
        }

        // Detailed debug logging
        Log.Debug("Audio data received: {ByteCount} bytes, Timestamp: {Timestamp}", 
            e.AudioData.Length, e.Timestamp);
    }

    private static void AnalyzeAudioData()
    {
        Console.WriteLine($"Audio Data Analysis:");
        Console.WriteLine($"  - Total events captured: {_capturedAudioLogs.Count}");
        Console.WriteLine($"  - Total bytes received: {_totalBytesReceived:N0}");
        
        if (_capturedAudioLogs.Count > 0)
        {
            var avgBytesPerEvent = _totalBytesReceived / _capturedAudioLogs.Count;
            var eventsPerSecond = _capturedAudioLogs.Count / 5.0; // 5 second test
            
            Console.WriteLine($"  - Average bytes per event: {avgBytesPerEvent:N0}");
            Console.WriteLine($"  - Events per second: {eventsPerSecond:F1}");
            Console.WriteLine($"  - Estimated data rate: {(_totalBytesReceived / 5.0):N0} bytes/second");
            
            // Audio quality assessment
            if (avgBytesPerEvent > 1000 && eventsPerSecond > 5)
            {
                Console.WriteLine("  ✓ Audio capture quality: GOOD");
            }
            else if (avgBytesPerEvent > 500 && eventsPerSecond > 2)
            {
                Console.WriteLine("  ⚠ Audio capture quality: MODERATE");
            }
            else
            {
                Console.WriteLine("  ✗ Audio capture quality: LOW (check microphone/system audio)");
            }
        }
        else
        {
            Console.WriteLine("  ✗ No audio data captured - check audio devices");
        }
    }

    private static void TestServiceCleanup(AudioCaptureService audioService)
    {
        try
        {
            Console.WriteLine("Testing service cleanup...");
            audioService.Dispose();
            Console.WriteLine("✓ Service disposed successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Service cleanup failed: {ex.Message}");
        }
    }
}
