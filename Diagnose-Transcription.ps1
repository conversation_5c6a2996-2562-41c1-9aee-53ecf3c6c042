# Transcription Diagnosis Script
param(
    [Parameter(Mandatory=$false)]
    [int]$MonitorSeconds = 30
)

Write-Host "=== Transcription Diagnosis Tool ===" -ForegroundColor Green
Write-Host "This will help diagnose why transcriptions aren't appearing" -ForegroundColor Yellow

function Test-ServiceStatus {
    Write-Host "`n--- Checking Service Status ---" -ForegroundColor Cyan
    
    # Check if service is running
    $dotnetProcesses = Get-Process -Name "dotnet" -ErrorAction SilentlyContinue
    if ($dotnetProcesses) {
        Write-Host "✓ Found $($dotnetProcesses.Count) .NET process(es) running" -ForegroundColor Green
        foreach ($proc in $dotnetProcesses) {
            Write-Host "  - PID: $($proc.Id), Memory: $([math]::Round($proc.WorkingSet/1MB, 1)) MB" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "✗ No .NET processes found - service may not be running" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-AudioActivity {
    Write-Host "`n--- Checking Audio Activity ---" -ForegroundColor Cyan
    
    # Check recent logs for audio data
    $logFiles = Get-ChildItem "logs\*.txt" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($logFiles) {
        Write-Host "Checking recent logs: $($logFiles.Name)" -ForegroundColor Yellow
        
        $recentLogs = Get-Content $logFiles.FullName -Tail 50
        
        # Look for audio capture indicators
        $audioEvents = $recentLogs | Where-Object { $_ -match "audio|Audio|data|Data" }
        if ($audioEvents.Count -gt 0) {
            Write-Host "✓ Found $($audioEvents.Count) audio-related log entries" -ForegroundColor Green
            Write-Host "Recent audio activity:" -ForegroundColor Gray
            $audioEvents | Select-Object -Last 3 | ForEach-Object {
                Write-Host "  $_" -ForegroundColor Gray
            }
        }
        else {
            Write-Host "⚠ No recent audio activity found in logs" -ForegroundColor Yellow
        }
        
        # Look for speech recognition events
        $speechEvents = $recentLogs | Where-Object { $_ -match "Recogniz|speech|Speech|transcript|Transcript" }
        if ($speechEvents.Count -gt 0) {
            Write-Host "✓ Found $($speechEvents.Count) speech recognition entries" -ForegroundColor Green
            Write-Host "Recent speech activity:" -ForegroundColor Gray
            $speechEvents | Select-Object -Last 5 | ForEach-Object {
                Write-Host "  $_" -ForegroundColor Green
            }
        }
        else {
            Write-Host "⚠ No speech recognition activity found" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "✗ No log files found" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-AzureConfiguration {
    Write-Host "`n--- Checking Azure Configuration ---" -ForegroundColor Cyan
    
    try {
        $config = Get-Content "appsettings.json" | ConvertFrom-Json
        
        $speechKey = $config.Azure.SpeechKey
        $speechRegion = $config.Azure.SpeechRegion
        
        if ($speechKey -and $speechKey.Length -gt 10 -and -not $speechKey.Contains("YOUR_") -and -not $speechKey.StartsWith("dummy")) {
            Write-Host "✓ Azure Speech Key appears to be configured" -ForegroundColor Green
            Write-Host "  Key: $($speechKey.Substring(0, 8))..." -ForegroundColor Gray
        }
        else {
            Write-Host "✗ Azure Speech Key not properly configured" -ForegroundColor Red
            Write-Host "  Current value: $speechKey" -ForegroundColor Gray
        }
        
        if ($speechRegion -and $speechRegion.Length -gt 0) {
            Write-Host "✓ Azure Speech Region: $speechRegion" -ForegroundColor Green
        }
        else {
            Write-Host "✗ Azure Speech Region not configured" -ForegroundColor Red
        }
        
        return ($speechKey -and $speechRegion)
    }
    catch {
        Write-Host "✗ Error reading configuration: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Start-LiveMonitoring {
    Write-Host "`n--- Starting Live Monitoring ---" -ForegroundColor Cyan
    Write-Host "Monitoring for $MonitorSeconds seconds..." -ForegroundColor Yellow
    Write-Host "Please speak into your microphone or play audio now!" -ForegroundColor White
    Write-Host "Press Ctrl+C to stop monitoring early" -ForegroundColor Gray
    
    $logFile = Get-ChildItem "logs\*.txt" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if (-not $logFile) {
        Write-Host "✗ No log file found for monitoring" -ForegroundColor Red
        return
    }
    
    Write-Host "Monitoring: $($logFile.Name)" -ForegroundColor Gray
    
    $startTime = Get-Date
    $lastSize = (Get-Item $logFile.FullName).Length
    $transcriptionCount = 0
    $audioEventCount = 0
    
    while ((Get-Date) -lt $startTime.AddSeconds($MonitorSeconds)) {
        try {
            $currentSize = (Get-Item $logFile.FullName).Length
            if ($currentSize -gt $lastSize) {
                # New content added
                $newContent = Get-Content $logFile.FullName -Tail 10
                
                foreach ($line in $newContent) {
                    if ($line -match "Recogniz.*:.*") {
                        $transcriptionCount++
                        Write-Host "🎙️ TRANSCRIPTION: $line" -ForegroundColor Green
                    }
                    elseif ($line -match "audio.*data|Audio.*data") {
                        $audioEventCount++
                        if ($audioEventCount % 10 -eq 0) {
                            Write-Host "🔊 Audio events: $audioEventCount" -ForegroundColor Cyan
                        }
                    }
                    elseif ($line -match "ERROR|Error|WARN|Warn") {
                        Write-Host "⚠️ $line" -ForegroundColor Yellow
                    }
                }
                
                $lastSize = $currentSize
            }
            
            Start-Sleep -Milliseconds 500
        }
        catch {
            Write-Host "Error monitoring: $($_.Exception.Message)" -ForegroundColor Red
            break
        }
    }
    
    Write-Host "`nMonitoring Summary:" -ForegroundColor Green
    Write-Host "  Transcriptions detected: $transcriptionCount" -ForegroundColor White
    Write-Host "  Audio events detected: $audioEventCount" -ForegroundColor White
    
    if ($transcriptionCount -eq 0 -and $audioEventCount -gt 0) {
        Write-Host "`n💡 Audio is being captured but no transcriptions found." -ForegroundColor Yellow
        Write-Host "   This could mean:" -ForegroundColor Yellow
        Write-Host "   1. Speech is too quiet or unclear" -ForegroundColor White
        Write-Host "   2. Azure Speech service is having issues" -ForegroundColor White
        Write-Host "   3. Audio format mismatch" -ForegroundColor White
    }
    elseif ($transcriptionCount -eq 0 -and $audioEventCount -eq 0) {
        Write-Host "`n⚠️ No audio or transcription activity detected." -ForegroundColor Red
        Write-Host "   Check if the service is actually running and capturing audio." -ForegroundColor White
    }
    elseif ($transcriptionCount -gt 0) {
        Write-Host "`n✅ Transcriptions are working! Check the console output of your service." -ForegroundColor Green
    }
}

function Show-TroubleshootingTips {
    Write-Host "`n--- Troubleshooting Tips ---" -ForegroundColor Cyan
    
    Write-Host "If you're not seeing transcriptions:" -ForegroundColor Yellow
    Write-Host "1. Make sure you're speaking clearly into the microphone" -ForegroundColor White
    Write-Host "2. Check microphone volume levels in Windows" -ForegroundColor White
    Write-Host "3. Verify the service console window is visible" -ForegroundColor White
    Write-Host "4. Try running: dotnet run --verbosity normal" -ForegroundColor White
    Write-Host "5. Check if Azure Speech service is working:" -ForegroundColor White
    Write-Host "   Test-NetConnection speech.platform.bing.com -Port 443" -ForegroundColor Gray
    
    Write-Host "`nTo see transcriptions in real-time:" -ForegroundColor Yellow
    Write-Host "1. Run the service with: dotnet run" -ForegroundColor White
    Write-Host "2. Watch the console output for 'Recognizing:' and 'Recognized:' messages" -ForegroundColor White
    Write-Host "3. Or monitor logs with: Get-Content logs\\*.txt -Wait -Tail 5" -ForegroundColor White
}

# Main execution
if (-not (Test-Path "appsettings.json")) {
    Write-Host "✗ Not in the correct directory. Please run from the project root." -ForegroundColor Red
    exit 1
}

$serviceRunning = Test-ServiceStatus
$audioActive = Test-AudioActivity
$azureConfigured = Test-AzureConfiguration

if ($serviceRunning -and $azureConfigured) {
    Start-LiveMonitoring
}
else {
    Write-Host "`n⚠️ Cannot start live monitoring due to issues above" -ForegroundColor Yellow
}

Show-TroubleshootingTips

Write-Host "`n=== Diagnosis Complete ===" -ForegroundColor Green
