using NAudio.Wave;
using NAudio.CoreAudioApi;
using System.Collections.Concurrent;

namespace InterviewAssistant.Service.Services;

public class AudioCaptureService : IDisposable
{
    private readonly ILogger<AudioCaptureService> _logger;
    private readonly IConfiguration _configuration;
    private WasapiCapture? _microphoneCapture;
    private WasapiLoopbackCapture? _systemAudioCapture;
    private readonly ConcurrentQueue<byte[]> _audioBuffer;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private Task? _processingTask;
    private bool _isCapturing;

    public event EventHandler<AudioDataEventArgs>? AudioDataAvailable;

    public AudioCaptureService(ILogger<AudioCaptureService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _audioBuffer = new ConcurrentQueue<byte[]>();
        _cancellationTokenSource = new CancellationTokenSource();
    }

    public async Task InitializeAsync()
    {
        try
        {
            _logger.LogInformation("Initializing dual audio capture service (Microphone + System Audio)");

            var enumerator = new MMDeviceEnumerator();

            // Get default microphone device
            var microphoneDevice = enumerator.GetDefaultAudioEndpoint(DataFlow.Capture, Role.Communications);
            _logger.LogInformation("Using microphone device: {DeviceName}", microphoneDevice.FriendlyName);

            // Get default speaker/output device for system audio capture
            var speakerDevice = enumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Communications);
            _logger.LogInformation("Using system audio device: {DeviceName}", speakerDevice.FriendlyName);

            // Configure audio format
            var sampleRate = _configuration.GetValue<int>("Audio:SampleRate", 16000);
            var channels = _configuration.GetValue<int>("Audio:Channels", 1);
            var bitsPerSample = _configuration.GetValue<int>("Audio:BitsPerSample", 16);

            // Initialize microphone capture
            _microphoneCapture = new WasapiCapture(microphoneDevice);
            _microphoneCapture.WaveFormat = new WaveFormat(sampleRate, bitsPerSample, channels);
            _microphoneCapture.DataAvailable += OnMicrophoneDataAvailable;
            _microphoneCapture.RecordingStopped += OnMicrophoneRecordingStopped;

            // Initialize system audio capture (loopback)
            _systemAudioCapture = new WasapiLoopbackCapture(speakerDevice);
            _systemAudioCapture.DataAvailable += OnSystemAudioDataAvailable;
            _systemAudioCapture.RecordingStopped += OnSystemAudioRecordingStopped;

            _logger.LogInformation("Dual audio capture initialized - Sample Rate: {SampleRate}Hz, Channels: {Channels}, Bits: {BitsPerSample}",
                sampleRate, channels, bitsPerSample);
            _logger.LogInformation("✓ Microphone capture: Will capture your voice");
            _logger.LogInformation("✓ System audio capture: Will capture Google Meet/Teams participants");

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize dual audio capture service");
            throw;
        }
    }

    public async Task StartCaptureAsync()
    {
        try
        {
            if (_microphoneCapture == null || _systemAudioCapture == null)
            {
                throw new InvalidOperationException("Audio capture not initialized. Call InitializeAsync first.");
            }

            _logger.LogInformation("Starting dual audio capture (Microphone + System Audio)");
            
            _isCapturing = true;
            
            // Start microphone capture
            _microphoneCapture.StartRecording();
            _logger.LogInformation("🎤 Microphone capture started");
            
            // Start system audio capture (loopback)
            _systemAudioCapture.StartRecording();
            _logger.LogInformation("🔊 System audio capture started");

            // Start audio processing task
            _processingTask = ProcessAudioBufferAsync(_cancellationTokenSource.Token);

            _logger.LogInformation("✅ Dual audio capture started successfully");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start dual audio capture");
            throw;
        }
    }

    public async Task StopCaptureAsync()
    {
        try
        {
            _logger.LogInformation("Stopping audio capture");

            _isCapturing = false;

            // Stop both microphone and system audio capture
            _microphoneCapture?.StopRecording();
            _systemAudioCapture?.StopRecording();

            _cancellationTokenSource.Cancel();

            if (_processingTask != null)
            {
                await _processingTask;
            }

            _logger.LogInformation("Audio capture stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping audio capture");
        }
    }

    private void OnMicrophoneDataAvailable(object? sender, WaveInEventArgs e)
    {
        if (_isCapturing && e.BytesRecorded > 0)
        {
            var audioData = new byte[e.BytesRecorded];
            Array.Copy(e.Buffer, audioData, e.BytesRecorded);
            _audioBuffer.Enqueue(audioData);
            _logger.LogDebug("Microphone data captured: {BytesRecorded} bytes", e.BytesRecorded);
        }
    }

    private void OnSystemAudioDataAvailable(object? sender, WaveInEventArgs e)
    {
        if (_isCapturing && e.BytesRecorded > 0)
        {
            var audioData = new byte[e.BytesRecorded];
            Array.Copy(e.Buffer, audioData, e.BytesRecorded);
            _audioBuffer.Enqueue(audioData);
            _logger.LogDebug("System audio data captured: {BytesRecorded} bytes", e.BytesRecorded);
        }
    }

    private void OnMicrophoneRecordingStopped(object? sender, StoppedEventArgs e)
    {
        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "Microphone recording stopped due to error");
        }
        else
        {
            _logger.LogInformation("Microphone recording stopped normally");
        }
    }

    private void OnSystemAudioRecordingStopped(object? sender, StoppedEventArgs e)
    {
        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "System audio recording stopped due to error");
        }
        else
        {
            _logger.LogInformation("System audio recording stopped normally");
        }
    }

    private void OnDataAvailable(object? sender, WaveInEventArgs e)
    {
        if (_isCapturing && e.BytesRecorded > 0)
        {
            var audioData = new byte[e.BytesRecorded];
            Array.Copy(e.Buffer, audioData, e.BytesRecorded);
            _audioBuffer.Enqueue(audioData);
        }
    }

    private void OnRecordingStopped(object? sender, StoppedEventArgs e)
    {
        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "Audio recording stopped due to error");
        }
        else
        {
            _logger.LogInformation("Audio recording stopped normally");
        }
    }

    private async Task ProcessAudioBufferAsync(CancellationToken cancellationToken)
    {
        var bufferMilliseconds = _configuration.GetValue<int>("Audio:BufferMilliseconds", 100);
        var delay = TimeSpan.FromMilliseconds(bufferMilliseconds);

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var audioChunks = new List<byte[]>();
                
                // Collect audio chunks from buffer
                while (_audioBuffer.TryDequeue(out var chunk))
                {
                    audioChunks.Add(chunk);
                }

                if (audioChunks.Count > 0)
                {
                    // Combine chunks into single buffer
                    var totalLength = audioChunks.Sum(chunk => chunk.Length);
                    var combinedBuffer = new byte[totalLength];
                    var offset = 0;

                    foreach (var chunk in audioChunks)
                    {
                        Array.Copy(chunk, 0, combinedBuffer, offset, chunk.Length);
                        offset += chunk.Length;
                    }

                    // Raise event with audio data
                    AudioDataAvailable?.Invoke(this, new AudioDataEventArgs(combinedBuffer));
                }

                await Task.Delay(delay, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing audio buffer");
                await Task.Delay(1000, cancellationToken);
            }
        }
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _microphoneCapture?.Dispose();
        _systemAudioCapture?.Dispose();
        _cancellationTokenSource.Dispose();
    }
}

public class AudioDataEventArgs : EventArgs
{
    public byte[] AudioData { get; }
    public DateTime Timestamp { get; }

    public AudioDataEventArgs(byte[] audioData)
    {
        AudioData = audioData;
        Timestamp = DateTime.UtcNow;
    }
}
